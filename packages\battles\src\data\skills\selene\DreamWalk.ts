import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Dream Walk',
  name: 'Dream Walk',
  element: '<PERSON>lene',
  manaCost: 16,
  cooldown: 3,
  description: 'Walk through dreams to avoid damage and strike with increased critical chance.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    // Enhanced critical chance for this skill
    const dreamCrit = isCritical || Math.random() < 0.25; // 25% bonus crit chance
    const finalDamage = dreamCrit ? Math.round(damage * 1.5) : damage;

    return {
      damage: finalDamage,
      isCritical: dreamCrit,
      appliedEffect: {
        id: 'dream_walk',
        name: 'Dream Walk',
        duration: 2,
        potency: 0.5, // 50% damage reduction (evasion)
        sourceId: caster.id,
      },
      log: `${caster.name} walks through dreams to strike ${target.name} for ${finalDamage} damage${dreamCrit ? ' (CRIT!)' : ''} and becomes ethereal!`,
    };
  },
});
