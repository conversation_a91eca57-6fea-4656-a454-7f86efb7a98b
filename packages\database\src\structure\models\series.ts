import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export class Series extends BaseModel {
  public schema = createSelectSchema(this.schemas.series);

  public async get(name: string) {
    const series = await this.client.instance.query.series.findFirst({
      where: {
        name,
      },
    });

    if (series) return series;

    const inserted = await this.client.instance
      .insert(this.schemas.series)
      .values({
        name,
      })
      .returning()
      .execute();

    return inserted[0]!;
  }
}
