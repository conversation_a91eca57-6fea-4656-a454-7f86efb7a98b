'use client';

import { MenuBar } from '@megami/ui/components/registry/bottom-menu';
import { MorphingDialog, useMorphingDialog } from '@megami/ui/components/registry/morphing-dialog';
import { Briefcase, Calendar, Home, SettingsIcon, Shield } from '@megami/ui/icons/lucide';
import { useRouter } from 'next/navigation';

import { Settings } from './settings';

export function NavbarOuter({ children }: { children: React.ReactNode }) {
  return (
    <MorphingDialog
      transition={{
        type: 'spring',
        bounce: 0,
        duration: 0.3,
      }}
    >
      {children}
    </MorphingDialog>
  );
}

export function NavbarInner() {
  const { isOpen, setIsOpen } = useMorphingDialog();
  const router = useRouter();

  const ITEMS = [
    { label: 'Home', icon: Home, action: () => router.push('/') },
    {
      label: 'Strategy',
      icon: Briefcase,
      action: () => router.push('/strategy'),
    },
    { label: 'Period', icon: Calendar, action: () => router.push('/period') },
    { label: 'Security', icon: Shield, action: () => router.push('/security') },
    {
      label: 'Settings',
      icon: SettingsIcon,
      action: () => setIsOpen((open) => !open),
    },
  ];

  return (
    <>
      <div className="fixed right-0 bottom-0 left-0 flex items-center justify-center p-6">
        <MenuBar items={ITEMS} />
      </div>

      <Settings isOpen={isOpen} />
    </>
  );
}

export function Navbar() {
  return (
    <NavbarOuter>
      <NavbarInner />
    </NavbarOuter>
  );
}
