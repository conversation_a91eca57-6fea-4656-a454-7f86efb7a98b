import type { CurrencyNames } from '@megami/config/lib/currencies';
import type { InferSelectModel } from 'drizzle-orm';
import { eq } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export type CurrencySelect = InferSelectModel<typeof import('../../schema/currencies').currencies>;
export type BalanceSelect = InferSelectModel<typeof import('../../schema/balances').balances>;

export interface UserCurrencyBalance extends BalanceSelect {
  currency: CurrencySelect;
}

export class Currencies extends BaseModel {
  public schema = createSelectSchema(this.schemas.currencies);
  public balanceSchema = createSelectSchema(this.schemas.balances);

  /**
   * Get all currencies for a specific user (instance method)
   * @param userId The user ID to get currencies for
   * @returns Array of user currency balances with currency details
   */
  public async getUserCurrencies(userId: string): Promise<UserCurrencyBalance[]> {
    return (await this.client.instance.query.balances.findMany({
      where: { userId },
      with: {
        currency: true,
      },
      orderBy: { createdAt: 'asc' },
    })) as UserCurrencyBalance[];
  }

  /**
   * Get a specific currency balance for a user
   * @param userId The user ID
   * @param currencyId The currency ID
   * @returns User currency balance with currency details, or undefined if not found
   */
  public async getUserCurrency(userId: string, currencyId: number): Promise<UserCurrencyBalance | undefined> {
    return (await this.client.instance.query.balances.findFirst({
      where: { userId, currencyId },
      with: {
        currency: true,
      },
    })) as UserCurrencyBalance | undefined;
  }

  /**
   * Get all available currencies
   * @returns Array of all currencies
   */
  public async getAllCurrencies(): Promise<CurrencySelect[]> {
    return await this.client.instance.query.currencies.findMany({
      orderBy: { tier: 'asc', name: 'asc' },
    });
  }

  /**
   * Get a currency by ID
   * @param id The currency ID
   * @returns Currency or undefined if not found
   */
  public async getCurrency(id: number): Promise<CurrencySelect | undefined> {
    return await this.client.instance.query.currencies.findFirst({
      where: { id },
    });
  }

  /**
   * Get a currency by name
   * @param name The currency name
   * @returns Currency or undefined if not found
   */
  public async getCurrencyByName(name: CurrencyNames): Promise<CurrencySelect | undefined> {
    return await this.client.instance.query.currencies.findFirst({
      where: { name },
    });
  }

  /**
   * Create or update a user's currency balance
   * @param userId The user ID
   * @param currencyId The currency ID
   * @param amount The amount to set
   * @returns The updated balance
   */
  public async setUserBalance(userId: string, currencyId: number, amount: number): Promise<BalanceSelect> {
    const existing = await this.client.instance.query.balances.findFirst({
      where: { userId, currencyId },
    });

    if (existing) {
      const [updated] = await this.client.instance
        .update(this.schemas.balances)
        .set({ amount, updatedAt: new Date() })
        .where(eq(this.schemas.balances.id, existing.id))
        .returning();
      return updated!;
    }
    const [created] = await this.client.instance
      .insert(this.schemas.balances)
      .values({ userId, currencyId, amount })
      .returning();
    return created!;
  }

  /**
   * Add to a user's currency balance
   * @param userId The user ID
   * @param currencyId The currency ID
   * @param amount The amount to add (can be negative to subtract)
   * @returns The updated balance
   */
  public async addToUserBalance(userId: string, currencyId: number, amount: number): Promise<BalanceSelect> {
    const existing = await this.client.instance.query.balances.findFirst({
      where: { userId, currencyId },
    });

    if (existing) {
      const newAmount = existing.amount + amount;
      const [updated] = await this.client.instance
        .update(this.schemas.balances)
        .set({ amount: newAmount, updatedAt: new Date() })
        .where(eq(this.schemas.balances.id, existing.id))
        .returning();
      return updated!;
    }
    const [created] = await this.client.instance
      .insert(this.schemas.balances)
      .values({ userId, currencyId, amount })
      .returning();
    return created!;
  }

  /**
   * Initialize default currencies for a new user
   * @param userId The user ID
   * @returns Array of created balances
   */
  public async initializeUserCurrencies(userId: string): Promise<BalanceSelect[]> {
    const currencies = await this.getAllCurrencies();

    // Use Promise.all to avoid await in loop
    const balances = await Promise.all(currencies.map((currency) => this.setUserBalance(userId, currency.id, 0)));

    return balances;
  }
}
