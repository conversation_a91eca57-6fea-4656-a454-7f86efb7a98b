import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON>lags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.schedules.modules.restart.name'))
    .setNameLocalizations(getObject('commands.management.modules.schedules.modules.restart.name'))
    .setDescription(translate('commands.management.modules.schedules.modules.restart.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.schedules.modules.restart.description'))
    .addBooleanOption((option) =>
      option
        .setName(translate('commands.management.modules.schedules.modules.restart.options.force.name'))
        .setNameLocalizations(getObject('commands.management.modules.schedules.modules.restart.options.force.name'))
        .setDescription(translate('commands.management.modules.schedules.modules.restart.options.force.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.schedules.modules.restart.options.force.description')
        )
        .setRequired(false)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const force = interaction.options.getBoolean('force');
    const scheduler = interaction.client.scheduler;

    try {
      // Get current status before restart
      const status = scheduler.getStatus();
      const runningTasks = scheduler.getRunningTasks();

      // Check if tasks are running and force is not enabled
      if (runningTasks.length > 0 && !force) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('⚠️ **Tasks Currently Running**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Running Tasks:** ${runningTasks.length}`),
          new TextDisplayBuilder().setContent('The following tasks are currently executing:'),
          ...runningTasks
            .slice(0, 5)
            .map((task) => new TextDisplayBuilder().setContent(`  🔄 **${task.name}** (${task.id})`)),
          ...(runningTasks.length > 5
            ? [new TextDisplayBuilder().setContent(`  ... and ${runningTasks.length - 5} more tasks`)]
            : []),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('Restarting now will terminate these tasks.'),
          new TextDisplayBuilder().setContent('To restart anyway, use the `force` option.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Show initial restart message
      const initialContainer = new MegamiContainer([
        new TextDisplayBuilder().setContent('🔄 **Restarting Scheduler...**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('**Status:** Stopping current scheduler...'),
        new TextDisplayBuilder().setContent(`**Tasks to Terminate:** ${runningTasks.length}`),
        new TextDisplayBuilder().setContent('Please wait while the scheduler restarts...'),
      ]);

      await interaction.editReply({
        components: [initialContainer],
        flags: MessageFlags.IsComponentsV2,
      });

      // Perform the restart
      const startTime = Date.now();
      const result = await scheduler.restart(force);
      const restartTime = Date.now() - startTime;

      if (result.success) {
        // Get new status after restart
        const newStatus = scheduler.getStatus();
        const newRunningTasks = scheduler.getRunningTasks();

        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('✅ **Scheduler Restarted Successfully**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Restart Time:** ${restartTime}ms`),
          new TextDisplayBuilder().setContent(
            `**Previous Status:** ${status.isRunning ? 'Running' : 'Stopped'} (${status.totalTasks} tasks)`
          ),
          new TextDisplayBuilder().setContent(
            `**New Status:** ${newStatus.isRunning ? '✅ Running' : '❌ Stopped'} (${newStatus.totalTasks} tasks)`
          ),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent('📊 **Restart Summary:**'),
          new TextDisplayBuilder().setContent(`**Tasks Terminated:** ${runningTasks.length}`),
          new TextDisplayBuilder().setContent(`**Tasks Restarted:** ${newRunningTasks.length}`),
          new TextDisplayBuilder().setContent(`**Enabled Tasks:** ${newStatus.enabledTasks}`),
          new TextDisplayBuilder().setContent(`**Total Tasks:** ${newStatus.totalTasks}`),
        ]);

        // Add information about tasks that started on restart
        if (newRunningTasks.length > 0) {
          container.components.push(
            new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
            new TextDisplayBuilder().setContent('🚀 **Tasks Started on Restart:**')
          );

          for (const task of newRunningTasks.slice(0, 5)) {
            container.components.push(new TextDisplayBuilder().setContent(`  ✅ **${task.name}** (${task.id})`));
          }

          if (newRunningTasks.length > 5) {
            container.components.push(
              new TextDisplayBuilder().setContent(`  ... and ${newRunningTasks.length - 5} more tasks`)
            );
          }
        }

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });

        // Log the successful restart
        interaction.client.logger.info(
          `Scheduler restarted by ${interaction.user.tag} (${restartTime}ms)${force ? ' (forced)' : ''}`
        );
      } else {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Scheduler Restart Failed**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Restart Time:** ${restartTime}ms`),
          new TextDisplayBuilder().setContent('**Status:** ❌ Restart Failed'),
          ...(result.error
            ? [
                new TextDisplayBuilder().setContent(
                  `**Error:** \`\`\`${result.error.slice(0, 500)}${result.error.length > 500 ? '...' : ''}\`\`\``
                ),
              ]
            : []),
          new TextDisplayBuilder().setContent('The scheduler may be in an inconsistent state.'),
          new TextDisplayBuilder().setContent('Check the logs and consider a bot restart if issues persist.'),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });

        // Log the failed restart
        interaction.client.logger.error(`Scheduler restart failed (${interaction.user.tag}):`, result.error);
      }
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Restart Error**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while restarting the scheduler.'),
        new TextDisplayBuilder().setContent('The scheduler may be in an inconsistent state.'),
        new TextDisplayBuilder().setContent('Please check the logs or restart the bot if issues persist.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error restarting scheduler:', error);
    }
  },
}));
