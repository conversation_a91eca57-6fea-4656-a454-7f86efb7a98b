export function generate() {
  const stats = {
    1: { health: 0, attack: 0, defense: 0, speed: 0, luck: 0 },
    2: { health: 0, attack: 0, defense: 0, speed: 0, luck: 0 },
    3: { health: 0, attack: 0, defense: 0, speed: 0, luck: 0 },
  };

  const ranges: Record<1 | 2 | 3, { min: number; max: number }> = {
    1: { min: 100, max: 150 },
    2: { min: 400, max: 500 },
    3: { min: 700, max: 1000 },
  };

  for (const tier of [1, 2, 3] as const) {
    const { min, max } = ranges[tier];
    stats[tier] = {
      health: Math.floor(Math.random() * (max - min + 1)) + min,
      attack: Math.floor(Math.random() * (max - min + 1)) + min,
      defense: Math.floor(Math.random() * (max - min + 1)) + min,
      speed: Math.floor(Math.random() * (max - min + 1)) + min,
      luck: Math.floor(Math.random() * (max - min + 1)) + min,
    };
  }

  return stats;
}
