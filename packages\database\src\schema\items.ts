import { boolean, integer, jsonb, pgTable, text, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';

export type ItemType =
  | 'CONSUMABLE' // Potions, food, temporary buffs
  | 'EQUIPMENT' // Weapons, armor, accessories
  | 'MATERIAL' // Crafting materials, upgrade stones
  | 'COLLECTIBLE' // Rare items, trophies, achievements
  | 'CURRENCY' // Special currencies, tokens
  | 'GIFT' // Gift boxes, packages
  | 'KEY' // Keys for unlocking content
  | 'MISC'; // Other items

export type ItemRarity = 'COMMON' | 'UNCOMMON' | 'RARE' | 'EPIC' | 'LEGENDARY' | 'MYTHIC';

export interface ItemMetadata {
  // Equipment stats (if applicable)
  stats?: {
    attack?: number;
    defense?: number;
    health?: number;
    speed?: number;
    luck?: number;
  };

  // Consumable effects (if applicable)
  effects?: {
    type: 'HEAL' | 'BUFF' | 'EXPERIENCE' | 'CURRENCY' | 'CUSTOM';
    value: number;
    duration?: number; // in seconds, for temporary effects
    target?: 'SELF' | 'PARTY' | 'ALL';
  }[];

  // Usage restrictions
  restrictions?: {
    levelRequired?: number;
    classRequired?: string[];
    oneTimeUse?: boolean;
    cooldown?: number; // in seconds
  };

  // Visual and flavor
  flavor?: {
    lore?: string;
    tags?: string[];
    color?: string; // hex color for UI theming
  };

  // Crafting and economy
  economy?: {
    sellPrice?: number;
    buyPrice?: number;
    craftingMaterials?: Array<{
      itemId: string;
      quantity: number;
    }>;
  };
}

export const items = pgTable(
  'items',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    description: text('description').notNull(),
    type: text('type').$type<ItemType>().notNull(),
    rarity: text('rarity').$type<ItemRarity>().notNull(),

    // Visual representation
    icon: text('icon'), // URL or emoji for the item icon
    image: text('image'), // Full image URL for detailed view

    // Core properties
    stackable: boolean('stackable').default(true).notNull(), // Can multiple be held in one slot
    maxStack: integer('max_stack').default(99).notNull(), // Maximum stack size
    tradeable: boolean('tradeable').default(true).notNull(), // Can be traded between users
    sellable: boolean('sellable').default(true).notNull(), // Can be sold to NPCs/system

    // Metadata for complex item behaviors
    metadata: jsonb('metadata').$type<ItemMetadata>().default({}).notNull(),

    // Status
    isActive: boolean('is_active').default(true).notNull(), // Can be obtained/used
    isHidden: boolean('is_hidden').default(false).notNull(), // Hidden from public lists

    // Timestamps
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [uniqueIndex('item_name_idx').on(table.name), uniqueIndex('item_id_idx').on(table.id)]
);
