import type { SkinRarity } from '@megami/config/lib/rewards';
import { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { characters } from './characters';
import { users } from './users';

export const skins = pgTable('skins', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  rarity: text('rarity').$type<SkinRarity>().notNull(),
  approved: boolean('approved').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  url: text('url').notNull(),

  // References:
  authorId: text('author_id')
    .references(() => users.id)
    .notNull(),
  characterId: uuid('character_id')
    .notNull()
    .references(() => characters.id),
});
