import { CronJob, validateCronExpression as validate } from 'cron';
import type { Logger } from 'winston';
import type { MegamiClient } from './client';

export interface ScheduledTask {
  id: string;
  name: string;
  expression: string;
  execute: (client: MegamiClient) => Promise<void> | void;
  enabled: boolean;
  runOnStart?: boolean;
}

export function defineTask(task: ScheduledTask): ScheduledTask {
  return task;
}

export interface SchedulerOptions {
  autoStart?: boolean;
}

export class MegamiScheduler {
  private client: MegamiClient;
  private logger: Logger;
  private tasks: Map<string, { task: ScheduledTask; job: CronJob }>;
  private isRunning: boolean;

  constructor(client: MegamiClient, options: SchedulerOptions = {}) {
    this.client = client;
    this.logger = client.logger;
    this.tasks = new Map();
    this.isRunning = false;
    this.options = {
      autoStart: true,
      ...options,
    };
  }

  public addTask(task: ScheduledTask): void {
    if (this.tasks.has(task.id)) {
      throw new Error(`Task with ID '${task.id}' already exists`);
    }

    if (!validate(task.expression)) {
      throw new Error(`Invalid cron expression: ${task.expression}`);
    }

    this.tasks.set(task.id, {
      task,
      job: new CronJob(
        task.expression,
        async () => {
          await this.executeTask(task);
        },
        undefined,
        false,
        'UTC'
      ),
    });

    this.logger.info(`Added scheduled task: ${task.name} (${task.id})`);

    if (this.isRunning && task.enabled) {
      this.scheduleTask(task.id);
    }
  }

  public removeTask(id: string): boolean {
    const task = this.tasks.get(id);
    if (!task) {
      return false;
    }

    if (task.job) {
      task.job.stop();
    }

    this.tasks.delete(id);
    this.logger.info(`Removed scheduled task: ${task.task.name} (${task.task.id})`);
    return true;
  }

  public enableTask(id: string): boolean {
    const task = this.tasks.get(id);
    if (!task) {
      return false;
    }

    task.task.enabled = true;

    if (this.isRunning) {
      this.scheduleTask(id);
    }

    this.logger.info(`Enabled scheduled task: ${task.task.name} (${id})`);
    return true;
  }

  public disableTask(id: string): boolean {
    const task = this.tasks.get(id);
    if (!task) {
      return false;
    }

    task.task.enabled = false;

    if (task.job) {
      task.job.stop();
    }

    this.logger.info(`Disabled scheduled task: ${task.task.name} (${id})`);
    return true;
  }

  public getTask(id: string): ScheduledTask | undefined {
    return this.tasks.get(id)?.task;
  }

  public getAllTasks(): ScheduledTask[] {
    return Array.from(this.tasks.values()).map((task) => task.task);
  }

  public getEnabledTasks(): ScheduledTask[] {
    return this.getAllTasks().filter((task) => task.enabled);
  }

  public getRunningTasks(): ScheduledTask[] {
    return Array.from(this.tasks.values())
      .filter((task) => task.task.enabled && task.job !== null)
      .map((task) => task.task);
  }

  public start(): void {
    if (this.isRunning) {
      this.logger.warn('Scheduler is already running');
      return;
    }

    this.isRunning = true;
    this.logger.info('Starting scheduler...');

    for (const [taskId, task] of this.tasks) {
      if (task.task.enabled) {
        this.scheduleTask(taskId);

        if (task.task.runOnStart) {
          this.executeTask(task.task).catch((error) => {
            this.logger.error(`Error running task on start: ${task.task.name}`, error);
          });
        }
      }
    }

    this.logger.info(`Scheduler started with ${this.getRunningTasks().length} active tasks`);
  }

  public stop(): void {
    if (!this.isRunning) {
      this.logger.warn('Scheduler is not running');
      return;
    }

    this.isRunning = false;
    this.logger.info('Stopping scheduler...');

    for (const [_taskId, task] of this.tasks) {
      if (task.job) {
        task.job.stop();
      }
    }

    this.logger.info('Scheduler stopped');
  }

  public restart(): void {
    this.stop();
    this.start();
  }

  public async executeTaskById(id: string): Promise<boolean> {
    const task = this.tasks.get(id);
    if (!task) {
      return false;
    }

    await this.executeTask(task.task);
    return true;
  }

  public getStatus(): {
    isRunning: boolean;
    totalTasks: number;
    enabledTasks: number;
    runningTasks: number;
  } {
    return {
      isRunning: this.isRunning,
      totalTasks: this.tasks.size,
      enabledTasks: this.getEnabledTasks().length,
      runningTasks: this.getRunningTasks().length,
    };
  }

  private scheduleTask(id: string): void {
    const task = this.tasks.get(id);
    if (!task?.task.enabled) {
      return;
    }

    // Stop existing cron task if any
    if (task.job) {
      task.job.stop();
    }

    try {
      task.job = new CronJob(
        task.task.expression,
        async () => {
          await this.executeTask(task.task);
        },
        undefined,
        true,
        'UTC'
      );

      this.logger.info(`Scheduled task: ${task.task.name} (${task.task.expression})`);
    } catch (error) {
      this.logger.error(`Failed to schedule task: ${task.task.name}`, error);
    }
  }

  private async executeTask(task: ScheduledTask): Promise<void> {
    const startTime = Date.now();
    this.logger.info(`Executing scheduled task: ${task.name}`);

    try {
      await task.execute(this.client);
      const duration = Date.now() - startTime;
      this.logger.info(`Completed scheduled task: ${task.name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to execute scheduled task: ${task.name} (${duration}ms)`, error);
    }
  }
}
