/* biome-ignore-all lint/style/useDefaultSwitchClause: Don't Need It */
import { formulas } from '../formulas';
import { SKILLS, STATUS_EFFECTS } from '../registry';
import type { BattleEntity, BattleLogEntry, PlayerAction } from '../types';

export class Battle {
  entities: Map<string, BattleEntity>;
  turnOrder: string[];
  currentTurnIndex: number;
  entries: BattleLogEntry[];
  isFinished: boolean;
  turn: number;
  roundOrder: string[];
  roundIndex: number;

  constructor(players: BattleEntity[], enemies: BattleEntity[]) {
    this.entities = new Map();

    for (const entity of [...players, ...enemies]) {
      this.entities.set(entity.id, { ...entity });
    }

    this.turnOrder = [];
    this.currentTurnIndex = 0;
    this.entries = [];
    this.isFinished = false;
    this.turn = 1;
    this.roundOrder = [];
    this.roundIndex = 0;

    this.setup();
  }

  setup() {
    // Only include alive entities
    const alive = Array.from(this.entities.values()).filter((e) => e.stats.health > 0);

    // Sort by speed descending, randomize ties
    this.roundOrder = alive
      .map((e) => ({ ...e, rand: Math.random() }))
      .sort((a, b) => b.stats.speed - a.stats.speed || a.rand - b.rand)
      .map((e) => e.id);

    this.roundIndex = 0;
  }

  start() {
    this.log('Battle begins!');
    this.setup();
    this.nextTurn();
  }

  getCurrentEntity(): BattleEntity {
    return this.entities.get(this.roundOrder[this.roundIndex as keyof typeof this.roundOrder] as string)!;
  }

  processTurn(action: PlayerAction) {
    if (this.isFinished) return;
    const entity = this.getCurrentEntity();
    let logMsg = '';
    let target: BattleEntity | undefined;
    if ('targetId' in action) {
      target = this.entities.get(action.targetId);
      if (!target || target.stats.health <= 0) {
        this.log('Invalid target.');
        return;
      }
      // Evasion check
      const evasionChance = formulas.calculateEvasionChance(entity, target);
      if (Math.random() * 100 < evasionChance) {
        this.log(`${target.name} evades the attack!`);
        this.endTurn();
        return;
      }
    }

    switch (action.type) {
      case 'BASIC_ATTACK': {
        const { damage, isCritical } = formulas.calculateDamage(entity, target!);
        target!.stats.health = Math.max(0, target!.stats.health - damage);
        logMsg = `${entity.name} attacks ${target!.name} for ${damage} damage${isCritical ? ' (CRIT!)' : ''}.`;
        break;
      }
      case 'SKILL': {
        const skill = SKILLS[action.skillId];
        if (!skill) throw new Error('Skill not found');
        if (entity.stats.mana < skill.manaCost) throw new Error('Not enough mana');
        if (entity.cooldowns[action.skillId] && entity.cooldowns[action.skillId as keyof typeof entity.cooldowns]! > 0)
          throw new Error('Skill on cooldown');
        const result = skill.execute(entity, target!);
        if (result.damage) target!.stats.health = Math.max(0, target!.stats.health - result.damage);
        if (result.appliedEffect) target!.activeEffects.push(result.appliedEffect);
        entity.stats.mana -= skill.manaCost;
        entity.cooldowns[action.skillId] = skill.cooldown;
        logMsg = result.log;
        break;
      }
      case 'HEAL': {
        const healAmount = Math.min(action.amount, entity.stats.maxHealth - entity.stats.health);
        entity.stats.health += healAmount;
        logMsg = `${entity.name} heals for ${healAmount} HP.`;
        break;
      }
    }
    this.log(logMsg);
    this.endTurn();
  }

  processEnemyTurn(enemy: BattleEntity) {
    // Simple AI: use skill if available and enough mana, else basic attack
    const skill = enemy.skill;
    let action: PlayerAction;
    const playerTargets = Array.from(this.entities.values()).filter((e) => e.isPlayer && e.stats.health > 0);
    const target = playerTargets[Math.floor(Math.random() * playerTargets.length)]!;

    if (skill.manaCost <= enemy.stats.mana) {
      action = { type: 'SKILL', skillId: skill.id, targetId: target.id };
    } else {
      action = { type: 'BASIC_ATTACK', targetId: target.id };
    }

    this.processTurn(action);
  }

  nextTurn() {
    if (this.isFinished) return;

    if (this.roundIndex >= this.roundOrder.length) {
      // End of round, re-calculate order for next round
      this.turn++;
      this.setup();
    }

    const entity = this.getCurrentEntity();
    this.applyStartOfTurnEffects(entity);
    this.decrementCooldownsAndEffects(entity);
    if (entity.stats.health <= 0) {
      this.log(`${entity.name} is defeated!`);
      this.checkBattleEnd();
      this.advanceTurn();
      return;
    }

    if (!entity.isPlayer) {
      this.processEnemyTurn(entity);
    }

    // For players, wait for external input (Discord interaction or CLI)
  }

  endTurn() {
    this.checkBattleEnd();
    this.advanceTurn();
  }

  advanceTurn() {
    if (this.isFinished) return;
    this.roundIndex++;

    if (this.roundIndex >= this.roundOrder.length) {
      // End of round, re-calculate order for next round
      this.turn++;
      this.setup();
    }

    this.nextTurn();
  }

  applyStartOfTurnEffects(entity: BattleEntity) {
    // Mana re-gen
    entity.stats.mana = Math.min(entity.stats.maxMana, entity.stats.mana + 5 + Math.floor(entity.stats.maxMana / 20));

    // Status effects
    for (const effect of entity.activeEffects) {
      const def = STATUS_EFFECTS[effect.id];
      if (def?.onTurnStart) {
        const result = def.onTurnStart(entity, effect);
        if (result.damage) {
          entity.stats.health = Math.max(0, entity.stats.health - result.damage);
        }
        if (result.log) {
          this.log(result.log);
        }
      }
    }
  }

  decrementCooldownsAndEffects(entity: BattleEntity) {
    // Cooldowns
    for (const skillId in entity.cooldowns) {
      if (entity.cooldowns[skillId]! > 0) entity.cooldowns[skillId]!--;
    }

    // Status effects
    entity.activeEffects = entity.activeEffects.filter((effect) => {
      effect.duration--;
      return effect.duration > 0;
    });
  }

  checkBattleEnd() {
    const playersAlive = Array.from(this.entities.values()).filter((e) => e.isPlayer && e.stats.health > 0);
    const enemiesAlive = Array.from(this.entities.values()).filter((e) => !e.isPlayer && e.stats.health > 0);
    if (playersAlive.length === 0) {
      this.isFinished = true;
      this.log('All players have been defeated!');
    } else if (enemiesAlive.length === 0) {
      this.isFinished = true;
      this.log('All enemies have been defeated!');
    }
  }

  log(message: string) {
    this.entries.push({ turn: this.turn, message });
  }
}
