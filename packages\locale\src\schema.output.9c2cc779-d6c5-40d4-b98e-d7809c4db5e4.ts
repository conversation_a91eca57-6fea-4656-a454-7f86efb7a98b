import { z } from "zod"

export default z.object({ "about": z.object({ "name": z.string(), "description": z.string() }), "daily": z.object({ "name": z.string(), "description": z.string() }), "weekly": z.object({ "name": z.string(), "description": z.string() }), "monthly": z.object({ "name": z.string(), "description": z.string() }), "register": z.object({ "name": z.string(), "description": z.string() }), "profile": z.object({ "name": z.string(), "description": z.string(), "modules": z.object({ "view": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "user": z.object({ "name": z.string(), "description": z.string() }) }) }) }) }), "inventory": z.object({ "name": z.string(), "description": z.string(), "modules": z.object({ "list": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "type": z.object({ "name": z.string(), "description": z.string() }) }) }), "shop": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "category": z.object({ "name": z.string(), "description": z.string() }) }) }), "use": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "item": z.object({ "name": z.string(), "description": z.string() }), "target": z.object({ "name": z.string(), "description": z.string() }) }) }), "gift": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "item": z.object({ "name": z.string(), "description": z.string() }), "recipient": z.object({ "name": z.string(), "description": z.string() }), "message": z.object({ "name": z.string(), "description": z.string() }) }) }) }) }), "management": z.object({ "name": z.string(), "description": z.string(), "modules": z.object({ "disable": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "command": z.object({ "name": z.string(), "description": z.string() }) }) }), "items": z.object({ "name": z.string(), "description": z.string(), "modules": z.object({ "create": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "name": z.object({ "name": z.string(), "description": z.string() }), "description": z.object({ "name": z.string(), "description": z.string() }), "type": z.object({ "name": z.string(), "description": z.string() }), "rarity": z.object({ "name": z.string(), "description": z.string() }) }) }), "delete": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "item": z.object({ "name": z.string(), "description": z.string() }), "confirm": z.object({ "name": z.string(), "description": z.string() }) }) }) }) }), "skins": z.object({ "name": z.string(), "description": z.string(), "modules": z.object({ "stats": z.object({ "name": z.string(), "description": z.string() }), "pending": z.object({ "name": z.string(), "description": z.string() }), "approve": z.object({ "name": z.string(), "description": z.string() }) }) }), "schedules": z.object({ "name": z.string(), "description": z.string(), "modules": z.object({ "status": z.object({ "name": z.string(), "description": z.string() }), "list": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "filter": z.object({ "name": z.string(), "description": z.string() }) }) }), "enable": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "task": z.object({ "name": z.string(), "description": z.string() }) }) }), "disable": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "task": z.object({ "name": z.string(), "description": z.string() }), "force": z.object({ "name": z.string(), "description": z.string() }) }) }), "execute": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "task": z.object({ "name": z.string(), "description": z.string() }), "force": z.object({ "name": z.string(), "description": z.string() }) }) }), "restart": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "force": z.object({ "name": z.string(), "description": z.string() }) }) }) }) }) }) }), "skins": z.object({ "name": z.string(), "description": z.string(), "modules": z.object({ "upload": z.object({ "name": z.string(), "description": z.string(), "options": z.object({ "character": z.object({ "name": z.string(), "description": z.string() }), "name": z.object({ "name": z.string(), "description": z.string() }), "image": z.object({ "name": z.string(), "description": z.string() }), "rarity": z.object({ "name": z.string(), "description": z.string() }) }) }) }) }) })
