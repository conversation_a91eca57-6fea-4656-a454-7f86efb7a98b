import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Quantum Flare',
  name: 'Quantum Flare',
  element: 'Arcane',
  manaCost: 22,
  cooldown: 3,
  description: 'A burst of quantum energy that deals heavy damage and may burn the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const baseChance = 35;
    const burnChance = formulas.calculateEffectChance(caster, target, baseChance);
    const didBurn = Math.random() * 100 < burnChance;
    return {
      damage: Math.round(damage * 1.3),
      isCritical,
      appliedEffect: didBurn
        ? {
            id: 'burn',
            name: 'Burn',
            duration: 2,
            potency: Math.round(caster.stats.attack * 0.25),
            sourceId: caster.id,
          }
        : undefined,
      log: `${caster.name} unleashes Quantum Flare on ${target.name} for ${Math.round(damage * 1.3)} damage${isCritical ? ' (CRIT!)' : ''}.${didBurn ? ` ${target.name} is burned!` : ''}`,
    };
  },
});
