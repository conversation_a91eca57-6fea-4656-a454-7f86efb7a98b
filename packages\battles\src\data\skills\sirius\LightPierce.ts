import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: '<PERSON> Pierce',
  name: '<PERSON> Pierce',
  element: 'Sirius',
  manaCost: 16,
  cooldown: 2,
  description: 'Pierce through darkness and illusion, dealing true damage that ignores defense.',
  execute: (caster, target, _formulas) => {
    // Calculate base damage but ignore defense for this skill
    const baseDamage = caster.stats.attack;
    const pierceDamage = Math.round(baseDamage * 1.1);

    // Check for critical hit manually
    const critChance = Math.max(5, Math.min(75, 5 + caster.stats.luck / 10));
    const isCritical = Math.random() * 100 < critChance;
    const finalDamage = isCritical ? Math.round(pierceDamage * 1.5) : pierceDamage;

    return {
      damage: finalDamage,
      isCritical,
      log: `${caster.name} pierces through ${target.name}'s defenses with <PERSON> Pierce for ${finalDamage} true damage${isCritical ? ' (CRIT!)' : ''}!`,
    };
  },
});
