import { z } from "zod"

export default z.object({ "user": z.object({ "unregistered": z.object({ "title": z.string(), "description": z.string() }), "registered": z.object({ "title": z.string(), "description": z.string() }) }), "general": z.object({ "unknown": z.object({ "title": z.string(), "description": z.string() }) }), "activities": z.object({ "daily": z.object({ "tooSoon": z.object({ "title": z.string(), "description": z.string() }) }), "weekly": z.object({ "tooSoon": z.object({ "title": z.string(), "description": z.string() }), "levelTooLow": z.object({ "title": z.string(), "description": z.string() }) }), "monthly": z.object({ "tooSoon": z.object({ "title": z.string(), "description": z.string() }), "levelTooLow": z.object({ "title": z.string(), "description": z.string() }) }) }) })
