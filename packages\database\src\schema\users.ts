import { integer, jsonb, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export interface UserActivity {
  daily: {
    streak: number;
    lastClaim: number;
  };
  weekly: {
    streak: number;
    lastClaim: number;
  };
  monthly: {
    streak: number;
    lastClaim: number;
  };
}

export const users = pgTable('users', {
  id: text('id').primaryKey(),
  level: integer('level').default(1).notNull(),
  experience: integer('experience').default(0).notNull(),
  rifts: integer('rifts').default(0).notNull(),
  activity: jsonb('activity')
    .$type<UserActivity>()
    .default({
      daily: { streak: 0, lastClaim: 0 },
      weekly: { streak: 0, lastClaim: 0 },
      monthly: { streak: 0, lastClaim: 0 },
    })
    .notNull(),
  pity: integer('pity').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
