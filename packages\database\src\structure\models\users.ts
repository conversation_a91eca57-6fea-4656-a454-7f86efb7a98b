import { eq, sql } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import type { UserActivity } from '../../schema/users';
import { BaseModel } from '../model';

export class Users extends BaseModel {
  public schema = createSelectSchema(this.schemas.users);

  public async get(id: string) {
    return await this.client.instance.query.users.findFirst({
      where: {
        id,
      },
    });
  }

  public async create(id: string) {
    const [user] = await this.client.instance.insert(this.schemas.users).values({ id }).returning().execute();
    return user!;
  }

  /**
   * Initialize a new user
   */
  // private async intialize(id: string): Promise<void> {
  //   await this.client.
  // }

  public async exists(id: string): Promise<boolean> {
    const user = await this.get(id);
    return user !== undefined;
  }

  public async getOrCreate(id: string) {
    const existingUser = await this.get(id);
    if (existingUser) {
      return { user: existingUser, isNew: false };
    }

    const newUser = await this.create(id);
    return { user: newUser, isNew: true };
  }

  public async updateActivity(id: string, activityType: keyof UserActivity, streak: number, lastClaim: number) {
    const user = await this.get(id);
    if (!user) throw new Error('User not found');

    const updatedActivity = {
      ...user.activity,
      [activityType]: {
        streak,
        lastClaim,
      },
    };

    await this.client.instance
      .update(this.schemas.users)
      .set({ activity: updatedActivity })
      .where(eq(this.schemas.users.id, id));

    return updatedActivity;
  }

  public async addRifts(id: string, rifts: number) {
    const user = await this.get(id);
    if (!user) throw new Error('User not found');

    await this.client.instance
      .update(this.schemas.users)
      .set({ rifts: sql`${this.schemas.users.rifts} + ${rifts}` })
      .where(eq(this.schemas.users.id, id));

    return user.rifts + rifts;
  }

  public async addExperience(id: string, experience: number) {
    const user = await this.get(id);
    if (!user) throw new Error('User not found');

    const newExperience = user.experience + experience;
    const newLevel = this.calculateLevel(newExperience);

    await this.client.instance
      .update(this.schemas.users)
      .set({
        experience: newExperience,
        level: newLevel,
      })
      .where(eq(this.schemas.users.id, id));

    return { experience: newExperience, level: newLevel, leveledUp: newLevel > user.level };
  }

  private calculateLevel(experience: number): number {
    return Math.floor(experience / 4000) + 1;
  }
}
