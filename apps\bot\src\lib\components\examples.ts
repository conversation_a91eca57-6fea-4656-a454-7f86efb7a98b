import type { ButtonInteraction, ChatInputCommandInteraction } from 'discord.js';
import { MegamiEmbed } from '../../helpers/embed';
import { confirm, createConfirmation } from './confirmation';

/**
 * Example: Simple delete confirmation
 */
export async function confirmDelete(interaction: ChatInputCommandInteraction, itemName: string): Promise<boolean> {
  return await confirm({
    interaction,
    content: {
      embeds: [
        new MegamiEmbed()
          .setTitle('🗑️ Confirm Deletion')
          .setDescription(`Are you sure you want to delete **${itemName}**?\n\n⚠️ This action cannot be undone.`)
          .setColor('#e74c3c'),
      ],
    },
    confirmText: 'Delete',
    cancelText: 'Keep',
    confirmEmoji: '🗑️',
    cancelEmoji: '🛡️',
    timeout: 30_000,
  });
}

/**
 * Example: Purchase confirmation with cost display
 */
export async function confirmPurchase(
  interaction: ChatInputCommandInteraction,
  itemName: string,
  cost: number,
  currency = 'coins'
): Promise<boolean> {
  return await confirm({
    interaction,
    content: {
      embeds: [
        new MegamiEmbed()
          .setTitle('💰 Confirm Purchase')
          .setDescription(`Do you want to purchase **${itemName}**?`)
          .addFields(
            {
              name: '💎 Cost',
              value: `${cost.toLocaleString()} ${currency}`,
              inline: true,
            },
            {
              name: '⚠️ Note',
              value: 'This will deduct the cost from your balance.',
              inline: true,
            }
          )
          .setColor('#f39c12'),
      ],
    },
    confirmText: 'Purchase',
    cancelText: 'Cancel',
    confirmEmoji: '💳',
    cancelEmoji: '❌',
    timeout: 45_000,
  });
}

/**
 * Example: Dangerous action confirmation with typing requirement
 */
export async function confirmDangerousAction(
  interaction: ChatInputCommandInteraction,
  actionDescription: string,
  consequences: string[]
): Promise<{ confirmed: boolean; userInteraction?: ButtonInteraction }> {
  const result = await createConfirmation({
    interaction,
    content: {
      embeds: [
        new MegamiEmbed()
          .setTitle('⚠️ Dangerous Action')
          .setDescription(`**Action:** ${actionDescription}\n\n**Consequences:**`)
          .addFields({
            name: '🚨 What will happen:',
            value: consequences.map((c) => `• ${c}`).join('\n'),
            inline: false,
          })
          .setColor('#e74c3c')
          .setFooter({ text: 'Think carefully before proceeding!' }),
      ],
    },
    confirmText: 'I understand, proceed',
    cancelText: 'Cancel',
    confirmEmoji: '⚠️',
    cancelEmoji: '🛡️',
    timeout: 60_000,
  });

  return {
    confirmed: result?.confirmed ?? false,
    userInteraction: result?.interaction,
  };
}

/**
 * Example: Multi-step confirmation for complex operations
 */
export async function confirmComplexOperation(
  interaction: ChatInputCommandInteraction,
  operationName: string,
  steps: string[],
  estimatedTime: string
): Promise<boolean> {
  return await confirm({
    interaction,
    content: {
      embeds: [
        new MegamiEmbed()
          .setTitle('🔄 Complex Operation')
          .setDescription(`**Operation:** ${operationName}`)
          .addFields(
            {
              name: '📋 Steps',
              value: steps.map((step, i) => `${i + 1}. ${step}`).join('\n'),
              inline: false,
            },
            {
              name: '⏱️ Estimated Time',
              value: estimatedTime,
              inline: true,
            },
            {
              name: '💡 Note',
              value: 'You can cancel this operation at any time.',
              inline: true,
            }
          )
          .setColor('#3498db'),
      ],
    },
    confirmText: 'Start Operation',
    cancelText: 'Cancel',
    confirmEmoji: '🚀',
    cancelEmoji: '❌',
    timeout: 30_000,
  });
}
