import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON>lags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';
import type { ScheduledTask } from '../../../../../structure/scheduler';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.schedules.modules.list.name'))
    .setNameLocalizations(getObject('commands.management.modules.schedules.modules.list.name'))
    .setDescription(translate('commands.management.modules.schedules.modules.list.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.schedules.modules.list.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.schedules.modules.list.options.filter.name'))
        .setNameLocalizations(getObject('commands.management.modules.schedules.modules.list.options.filter.name'))
        .setDescription(translate('commands.management.modules.schedules.modules.list.options.filter.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.schedules.modules.list.options.filter.description')
        )
        .setRequired(false)
        .addChoices(
          { name: 'All Tasks', value: 'all' },
          { name: 'Enabled Only', value: 'enabled' },
          { name: 'Disabled Only', value: 'disabled' },
          { name: 'Running Only', value: 'running' }
        )
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const scheduler = interaction.client.scheduler;
    const filter = interaction.options.getString('filter') || 'all';

    let tasks: ScheduledTask[];
    switch (filter) {
      case 'enabled':
        tasks = scheduler.getEnabledTasks();
        break;
      case 'disabled':
        tasks = scheduler.getAllTasks().filter((task) => !task.enabled);
        break;
      case 'running':
        tasks = scheduler.getRunningTasks();
        break;
      default:
        tasks = scheduler.getAllTasks();
    }

    if (tasks.length === 0) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('📅 **Scheduled Tasks**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`No ${filter === 'all' ? '' : `${filter} `}tasks found.`),
        new TextDisplayBuilder().setContent('Tasks will appear here once they are created.'),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const components = [
      new TextDisplayBuilder().setContent(`📅 **Scheduled Tasks** (${filter === 'all' ? 'All' : filter})`),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent(`**Total:** ${tasks.length} task${tasks.length !== 1 ? 's' : ''}`),
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
    ];

    // Group tasks by status for better organization
    const enabledTasks = tasks.filter((task) => task.enabled);
    const disabledTasks = tasks.filter((task) => !task.enabled);

    if (enabledTasks.length > 0 && filter !== 'disabled') {
      components.push(new TextDisplayBuilder().setContent('✅ **Enabled Tasks:**'));

      for (const task of enabledTasks.slice(0, 8)) {
        const isRunning = scheduler.getRunningTasks().some((rt) => rt.id === task.id);
        const nextRun = getNextRunTime(task.cronExpression);
        const runOnStartIcon = task.runOnStart ? ' 🚀' : '';

        components.push(
          new TextDisplayBuilder().setContent(
            `  ${isRunning ? '🔄' : '⏸️'} **${task.name}** (${task.id})${runOnStartIcon}`
          ),
          new TextDisplayBuilder().setContent(`    └ \`${task.cronExpression}\` | Next: ${nextRun}`)
        );

        if (task.description) {
          components.push(new TextDisplayBuilder().setContent(`    └ ${task.description}`));
        }
      }

      if (enabledTasks.length > 8) {
        components.push(new TextDisplayBuilder().setContent(`  ... and ${enabledTasks.length - 8} more enabled tasks`));
      }

      if (disabledTasks.length > 0 && filter === 'all') {
        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      }
    }

    if (disabledTasks.length > 0 && filter !== 'enabled' && filter !== 'running') {
      components.push(new TextDisplayBuilder().setContent('❌ **Disabled Tasks:**'));

      for (const task of disabledTasks.slice(0, 5)) {
        const runOnStartIcon = task.runOnStart ? ' 🚀' : '';

        components.push(
          new TextDisplayBuilder().setContent(`  ⏹️ **${task.name}** (${task.id})${runOnStartIcon}`),
          new TextDisplayBuilder().setContent(`    └ \`${task.cronExpression}\` | Status: Disabled`)
        );

        if (task.description) {
          components.push(new TextDisplayBuilder().setContent(`    └ ${task.description}`));
        }
      }

      if (disabledTasks.length > 5) {
        components.push(
          new TextDisplayBuilder().setContent(`  ... and ${disabledTasks.length - 5} more disabled tasks`)
        );
      }
    }

    // Add legend
    components.push(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent('**Legend:**'),
      new TextDisplayBuilder().setContent('🔄 Currently Running | ⏸️ Enabled but Idle | ⏹️ Disabled'),
      new TextDisplayBuilder().setContent('🚀 Runs on Startup | ✅ Enabled | ❌ Disabled')
    );

    // Add quick stats
    const runningCount = scheduler.getRunningTasks().length;
    components.push(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      new TextDisplayBuilder().setContent('📊 **Quick Stats:**'),
      new TextDisplayBuilder().setContent(
        `**Running:** ${runningCount} | **Enabled:** ${enabledTasks.length} | **Disabled:** ${disabledTasks.length}`
      )
    );

    const container = new MegamiContainer(components);
    await interaction.editReply({
      components: [container],
      flags: MessageFlags.IsComponentsV2,
    });
  },
}));

function getNextRunTime(cronExpression: string): string {
  try {
    // This is a simplified next run calculation
    // In a real implementation, you'd use a cron parser library like 'node-cron' or 'cron-parser'
    const now = new Date();

    // Basic parsing for common patterns
    if (cronExpression === '* * * * *') {
      const nextRun = new Date(now.getTime() + 60_000);
      return `<t:${Math.floor(nextRun.getTime() / 1000)}:R>`;
    }

    if (cronExpression === '*/5 * * * *') {
      const minutes = now.getMinutes();
      const nextMinute = Math.ceil(minutes / 5) * 5;
      const nextRun = new Date(now);
      nextRun.setMinutes(nextMinute, 0, 0);
      if (nextRun <= now) nextRun.setHours(nextRun.getHours() + 1, 0, 0, 0);
      return `<t:${Math.floor(nextRun.getTime() / 1000)}:R>`;
    }

    if (cronExpression === '0 0 * * *') {
      const nextRun = new Date(now);
      nextRun.setDate(nextRun.getDate() + 1);
      nextRun.setHours(0, 0, 0, 0);
      return `<t:${Math.floor(nextRun.getTime() / 1000)}:R>`;
    }

    // Fallback for unknown patterns
    return 'Next execution calculated by scheduler';
  } catch {
    return 'Unknown';
  }
}
