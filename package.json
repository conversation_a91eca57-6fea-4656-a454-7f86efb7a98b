{"name": "@megami/root", "type": "module", "packageManager": "bun@1.2.19", "workspaces": ["apps/*", "packages/*"], "scripts": {"format": "bunx --bun ultracite format --unsafe", "lint": "bunx --bun ultracite lint", "taze": "taze", "typecheck": "bunx --bun turbo typecheck", "start": "bunx --bun turbo start"}, "dependencies": {"next": "^15.4.4"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@types/bun": "^1.2.19", "@types/node": "^24.1.0", "esbuild": "^0.25.8", "taze": "^19.1.0", "turbo": "^2.5.5", "typescript": "^5.8.3", "ultracite": "5.1.2", "vercel": "^44.6.4"}}