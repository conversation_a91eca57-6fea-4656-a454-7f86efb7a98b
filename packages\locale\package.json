{"name": "@megami/locale", "module": "src/index.ts", "main": "src/index.ts", "types": "src/index.ts", "type": "module", "private": true, "scripts": {"translate": "bunx i18n-ai-translate translate -i src/locales/en -o fr es da de id it fr hr hu lt nl no pl ro  -e gemini -m gemini-2.5-flash-lite-preview-06-17"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/groq": "^1.2.9", "@vitalets/google-translate-api": "^9.2.1", "ai": "^4.3.19", "i18n-ai-translate": "^4.1.2", "i18next": "^25.3.2", "json-schema-to-zod": "^2.6.1", "ky": "^1.8.2", "to-json-schema": "^0.2.5", "zod": "^4.0.13"}, "devDependencies": {"@types/bun": "latest", "@types/to-json-schema": "^0.2.4"}}