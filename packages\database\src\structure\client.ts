import { type Database, defineDatabase } from '../database';
import { Artists } from './models/artists';
import { Characters } from './models/characters';
import type { Currencies } from './models/currencies';
import { Frames } from './models/frames';
import { Holdings } from './models/holdings';
import { Images } from './models/images';
import { Items } from './models/items';
import { Notifications } from './models/notifications';
import { Series } from './models/series';
import { Skins } from './models/skins';
import { Users } from './models/users';
import { Vessels } from './models/vessels';

export interface MegamiDatabaseClientOptions {
  url: string;
}

export class MegamiDatabaseClient {
  public options: MegamiDatabaseClientOptions;
  public instance: Database;

  public artists: Artists;
  public characters: Characters;
  public currencies: Currencies;
  public frames: Frames;
  public holdings: Holdings;
  public images: Images;
  public items: Items;
  public notifications: Notifications;
  public series: Series;
  public skins: Skins;
  public users: Users;
  public vessels: Vessels;

  constructor(options: MegamiDatabaseClientOptions) {
    this.options = options;
    this.instance = defineDatabase(this.options.url);

    this.artists = new Artists(this);
    this.characters = new Characters(this);
    this.currencies = new Currencies(this);
    this.frames = new Frames(this);
    this.holdings = new Holdings(this);
    this.images = new Images(this);
    this.items = new Items(this);
    this.notifications = new Notifications(this);
    this.series = new Series(this);
    this.skins = new Skins(this);
    this.users = new Users(this);
    this.vessels = new Vessels(this);
  }
}
