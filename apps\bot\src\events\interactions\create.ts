import { is, team } from '@megami/config/lib/team';
import { toProperCase } from '@megami/utils/lib/case';
import { defineEvent } from '../../handlers/event';

export default defineEvent({
  name: 'interactionCreate',
  once: false,
  execute: async (interaction) => {
    if (interaction.isChatInputCommand()) {
      const command = interaction.client.storage.commands.get(interaction.commandName);
      if (!command) return;

      // const grouped = command.builder.options
      //   .map((option) => option.toJSON())
      //   .find(
      //     (option) =>
      //       option.type === ApplicationCommandOptionType.SubcommandGroup &&
      //       option.name === interaction.options.getSubcommandGroup()
      //   ) as ApplicationCommandSubGroup;

      // const sub =
      //   grouped && grouped.options
      //     ? grouped.options.find(
      //         (option) =>
      //           option.type === ApplicationCommandOptionType.Subcommand &&
      //           option.name === interaction.options.getSubcommand()
      //       )
      //     : command.builder.options
      //         .map((option) => option.toJSON())
      //         .find(
      //           (option) =>
      //             option.type === ApplicationCommandOptionType.Subcommand &&
      //             option.name === interaction.options.getSubcommand()
      //         );

      if (command.config.only) {
        const check = is(interaction.user.id, toProperCase(command.config.only));
        if (!check) {
          return await interaction.reply({
            content: 'You do not have permission to use this command.',
            ephemeral: true,
          });
        }
      }

      if (command.config.only?.includes('owner')) {
        const owners = team.filter((member) => member.role === 'Owner');
        if (!owners.find((member) => member.id === interaction.user.id)) {
          return await interaction.reply({
            content: 'This command is only available to the bot owner.',
            ephemeral: true,
          });
        }
      }

      if (command.config.only?.includes('management')) {
        const managers = team.filter((member) => member.role === 'Manager');
        if (!managers.find((member) => member.id === interaction.user.id)) {
          return await interaction.reply({
            content: 'This command is only available to server managers.',
            ephemeral: true,
          });
        }
      }

      if (command.config.only?.includes('developer')) {
        const developers = team.filter((member) => member.role === 'Developer');
        if (!developers.find((member) => member.id === interaction.user.id)) {
          return await interaction.reply({
            content: 'This command is only available to developers.',
            ephemeral: true,
          });
        }
      }

      if (command.config.only?.includes('artist')) {
        const artists = team.filter((member) => member.role === 'Artist');
        if (!artists.find((member) => member.id === interaction.user.id)) {
          return await interaction.reply({
            content: 'This command is only available to artists.',
            ephemeral: true,
          });
        }
      }

      await command.execute(interaction);
    }
  },
});
