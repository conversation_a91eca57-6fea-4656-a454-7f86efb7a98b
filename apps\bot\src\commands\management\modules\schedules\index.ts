import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineSubCommandGroup } from '../../../../handlers/command';
import disable from './modules/disable';
import enable from './modules/enable';
import execute from './modules/execute';
import list from './modules/list';
import restart from './modules/restart';
import status from './modules/status';

export default defineSubCommandGroup((builder) => ({
  builder: builder
    .setName(translate('commands.management.schedules.name'))
    .setNameLocalizations(getObject('commands.management.schedules.name'))
    .setDescription(translate('commands.management.schedules.description'))
    .setDescriptionLocalizations(getObject('commands.management.schedules.description'))
    .addSubcommand(status.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(list.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(enable.builder as <PERSON>lashCommandSubcommandBuilder)
    .addSubcommand(disable.builder as <PERSON><PERSON>CommandSubcommandBuilder)
    .addSubcommand(execute.builder as SlashCommandSubcommandBuilder)
    .addSubcommand(restart.builder as SlashCommandSubcommandBuilder),
  config: {},
  execute: async (interaction) => {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case 'status': {
        await status.execute(interaction);
        break;
      }

      case 'list': {
        await list.execute(interaction);
        break;
      }

      case 'enable': {
        await enable.execute(interaction);
        break;
      }

      case 'disable': {
        await disable.execute(interaction);
        break;
      }

      case 'execute': {
        await execute.execute(interaction);
        break;
      }

      case 'restart': {
        await restart.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: 'Unknown subcommand.',
          ephemeral: true,
        });
      }
    }
  },
}));
