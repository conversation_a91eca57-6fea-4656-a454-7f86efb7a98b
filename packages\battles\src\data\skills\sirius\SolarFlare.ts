import { defineSkill } from '../defineSkill';

export default defineSkill({
  id: 'Solar Flare',
  name: 'Solar Flare',
  element: 'Sirius',
  manaCost: 20,
  cooldown: 3,
  description: 'Unleash a solar flare that deals heavy damage and may blind the target.',
  execute: (caster, target, formulas) => {
    const { damage, isCritical } = formulas.calculateDamage(caster, target);
    const baseChance = 45;
    const blindChance = formulas.calculateEffectChance(caster, target, baseChance);
    const didBlind = Math.random() * 100 < blindChance;

    return {
      damage: Math.round(damage * 1.4),
      isCritical,
      appliedEffect: didBlind
        ? {
            id: 'blind',
            name: 'Blinded',
            duration: 2,
            potency: -15, // -15 accuracy/luck
            sourceId: caster.id,
          }
        : undefined,
      log: `${caster.name} unleashes a Solar Flare on ${target.name} for ${Math.round(damage * 1.4)} damage${isCritical ? ' (CRIT!)' : ''}${didBlind ? ' and blinds them!' : '!'}`,
    };
  },
});
