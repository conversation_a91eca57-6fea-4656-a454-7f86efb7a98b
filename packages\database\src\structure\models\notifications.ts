import { sql } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import { BaseModel } from '../model';

export type NotificationType = 'ANNOUNCEMENT' | 'CHANGELOG' | 'INFO';

export interface CreateNotificationInput {
  key: 'global' | string;
  title: string;
  content: string;
  type?: NotificationType;
}

export class Notifications extends BaseModel {
  public schema = createSelectSchema(this.schemas.notifications);

  public async create(data: CreateNotificationInput) {
    const [notification] = await this.client.instance
      .insert(this.schemas.notifications)
      .values({
        key: data.key,
        title: data.title,
        content: data.content,
        type: data.type || 'ANNOUNCEMENT',
        updatedAt: new Date(),
      })
      .returning()
      .execute();

    if (!notification) return;

    if (data.key === 'global') {
      await this.client.instance.execute(
        sql`INSERT INTO mails (user_id, notification_id) SELECT id AS user_id, ${notification.id} AS notification_id FROM users;`
      );
    } else {
      await this.client.instance.insert(this.schemas.mails).values({
        userId: data.key,
        notificationId: notification.id,
      });
    }

    return notification;
  }
}
