import * as activity from '@megami/config/lib/activity';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineCommand } from '../handlers/command';
import { MegamiContainer } from '../helpers/containers';
import { defer } from '../helpers/defer';
import { MegamiEmbed } from '../helpers/embed';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.daily.name'))
    .setNameLocalizations(getObject('commands.daily.name'))
    .setDescription(translate('commands.daily.description'))
    .setDescriptionLocalizations(getObject('commands.daily.description')),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      return await interaction.editReply({
        embeds: [
          new MegamiEmbed()
            .setTitle(translate('errors.user.unregistered.title'))
            .setDescription(translate('errors.user.unregistered.description')),
        ],
      });
    }

    const activityType = 'daily';
    const lastClaim = user.activity.daily.lastClaim;
    const currentStreak = user.activity.daily.streak;

    // Check if user can claim
    const claimCheck = activity.canClaimActivity(activityType, user.level, lastClaim);
    if (!claimCheck.canClaim) {
      return await interaction.editReply({
        embeds: [
          new MegamiEmbed()
            .setTitle(translate('errors.activities.daily.tooSoon.title'))
            .setDescription(translate('errors.activities.daily.tooSoon.description')),
        ],
      });
    }

    // Calculate new streak
    const now = Date.now();
    const cooldownMs = activity.activityConfigs.daily.cooldownHours * 60 * 60 * 1000;
    const gracePeriodMs = cooldownMs * 0.5;
    const timeSinceLastClaim = now - lastClaim;

    let newStreak = 1;
    if (lastClaim > 0 && timeSinceLastClaim <= cooldownMs + gracePeriodMs) {
      newStreak = currentStreak + 1;
    }

    // Calculate rewards
    const reward = activity.calculateReward(activityType, newStreak);

    try {
      // Update user activity
      await interaction.client.database.users.updateActivity(user.id, activityType, newStreak, now);

      // Add tickets and experience
      await interaction.client.database.users.addRifts(user.id, reward.rifts);
      const expResult = await interaction.client.database.users.addExperience(user.id, reward.experience);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent("You've claimed your daily reward!"),
        new TextDisplayBuilder().setContent(`Current streak: **${newStreak}** day${newStreak > 1 ? 's' : ''}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**${reward.rifts}** Rifts`),
        new TextDisplayBuilder().setContent(
          `**+${reward.experience}** XP${expResult.leveledUp ? `\n🎉 **Level Up!** You're now level ${expResult.level}!` : ''}`
        ),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          newStreak > 1
            ? `**+${Math.floor((newStreak - 1) * activity.activityConfigs.daily.streakBonus.multiplier * 100)}%** bonus`
            : 'Start your streak!'
        ),
      ]);

      await interaction.editReply({ components: [container], flags: MessageFlags.IsComponentsV2 });
    } catch (_error) {
      await interaction.editReply({
        embeds: [
          new MegamiEmbed()
            .setTitle(translate('errors.general.unknown.title'))
            .setDescription(translate('errors.general.unknown.description')),
        ],
      });
    }
  },
}));
