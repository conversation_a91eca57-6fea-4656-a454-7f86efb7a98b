import * as activity from '@megami/config/lib/activity';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineCommand } from '../handlers/command';
import { MegamiContainer } from '../helpers/containers';
import { defer } from '../helpers/defer';
import { MegamiEmbed } from '../helpers/embed';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.weekly.name'))
    .setNameLocalizations(getObject('commands.weekly.name'))
    .setDescription(translate('commands.weekly.description'))
    .setDescriptionLocalizations(getObject('commands.weekly.description')),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      return await interaction.editReply({
        embeds: [
          new MegamiEmbed()
            .setTitle(translate('errors.user.unregistered.title'))
            .setDescription(translate('errors.user.unregistered.description')),
        ],
      });
    }

    const activityType = 'weekly';
    const lastClaim = user.activity.weekly.lastClaim;
    const currentStreak = user.activity.weekly.streak;

    // Check if user can claim
    const claimCheck = activity.canClaimActivity(activityType, user.level, lastClaim);
    if (!claimCheck.canClaim) {
      return await interaction.editReply({
        embeds: [
          new MegamiEmbed()
            .setTitle(translate('errors.activities.weekly.tooSoon.title'))
            .setDescription(translate('errors.activities.weekly.tooSoon.description')),
        ],
      });
    }

    // Calculate new streak
    const now = Date.now();
    const cooldownMs = activity.activityConfigs.weekly.cooldownHours * 60 * 60 * 1000;
    const gracePeriodMs = cooldownMs * 0.5;
    const timeSinceLastClaim = now - lastClaim;

    let newStreak = 1;
    if (lastClaim > 0 && timeSinceLastClaim <= cooldownMs + gracePeriodMs) {
      newStreak = currentStreak + 1;
    }

    // Calculate rewards
    const reward = activity.calculateReward(activityType, newStreak);

    try {
      // Update user activity
      await interaction.client.database.users.updateActivity(user.id, activityType, newStreak, now);

      // Add rifts and experience
      await interaction.client.database.users.addRifts(user.id, reward.rifts);
      const expResult = await interaction.client.database.users.addExperience(user.id, reward.experience);

      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent("You've claimed your weekly reward!"),
        new TextDisplayBuilder().setContent(`Current streak: **${newStreak}** week${newStreak > 1 ? 's' : ''}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`**${reward.rifts}** Rifts`),
        new TextDisplayBuilder().setContent(
          `**+${reward.experience}** XP${expResult.leveledUp ? `\n🎉 **Level Up!** You're now level ${expResult.level}!` : ''}`
        ),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(
          newStreak > 1
            ? `**+${Math.floor((newStreak - 1) * activity.activityConfigs.weekly.streakBonus.multiplier * 100)}%** bonus`
            : 'Start your streak!'
        ),
      ]);

      await interaction.editReply({ components: [container], flags: MessageFlags.IsComponentsV2 });
    } catch (_error) {
      await interaction.editReply({
        embeds: [
          new MegamiEmbed()
            .setTitle(translate('errors.general.unknown.title'))
            .setDescription(translate('errors.general.unknown.description')),
        ],
      });
    }
  },
}));
