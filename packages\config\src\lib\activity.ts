export interface ActivityReward {
  rifts: number;
  experience: number;
}

export interface ActivityConfig {
  name: string;
  description: string;
  cooldownHours: number;
  levelRequirement: number;
  baseReward: ActivityReward;
  streakBonus: {
    multiplier: number;
    maxStreak: number;
  };
}

export const activityConfigs = {
  daily: {
    name: 'Daily Reward',
    description: 'Claim your daily rewards to keep your streak going!',
    cooldownHours: 24,
    levelRequirement: 1,
    baseReward: {
      rifts: 25,
      experience: 100,
    },
    streakBonus: {
      multiplier: 0.25, // % bonus per streak day
      maxStreak: 7, // Max 70% bonus
    },
  },
  weekly: {
    name: 'Weekly Reward',
    description: 'Claim your weekly rewards! Available at level 3.',
    cooldownHours: 168, // 7 days * 24 hours
    levelRequirement: 3,
    baseReward: {
      rifts: 50,
      experience: 750,
    },
    streakBonus: {
      multiplier: 0.15, // 15% bonus per streak week
      maxStreak: 4, // Max 60% bonus
    },
  },
  monthly: {
    name: 'Monthly Reward',
    description: 'Claim your monthly rewards! Available at level 5.',
    cooldownHours: 720, // 30 days * 24 hours
    levelRequirement: 5,
    baseReward: {
      rifts: 100,
      experience: 3000,
    },
    streakBonus: {
      multiplier: 0.2, // 20% bonus per streak month
      maxStreak: 3, // Max 60% bonus
    },
  },
} as const;

export type ActivityType = keyof typeof activityConfigs;

export function calculateReward(activityType: ActivityType, streak: number): ActivityReward {
  const config = activityConfigs[activityType];
  const effectiveStreak = Math.min(streak, config.streakBonus.maxStreak);
  const multiplier = 1 + effectiveStreak * config.streakBonus.multiplier;

  return {
    rifts: Math.floor(config.baseReward.rifts * multiplier),
    experience: Math.floor(config.baseReward.experience * multiplier),
  };
}

export function canClaimActivity(
  activityType: ActivityType,
  userLevel: number,
  lastClaimTimestamp: number
): { canClaim: boolean; reason?: string; nextClaimTime?: number } {
  const config = activityConfigs[activityType];
  const now = Date.now();
  const cooldownMs = config.cooldownHours * 60 * 60 * 1000;
  const nextClaimTime = lastClaimTimestamp + cooldownMs;

  // Check level requirement
  if (userLevel < config.levelRequirement) {
    return {
      canClaim: false,
      reason: `You need to be level ${config.levelRequirement} to claim ${config.name.toLowerCase()}.`,
    };
  }

  // Check cooldown
  if (now < nextClaimTime) {
    return {
      canClaim: false,
      reason: `You can claim your next ${config.name.toLowerCase()} in ${formatTimeRemaining(nextClaimTime - now)}.`,
      nextClaimTime,
    };
  }

  return { canClaim: true };
}

export function formatTimeRemaining(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
}

export function calculateStreak(lastClaimTimestamp: number, cooldownHours: number): number {
  const now = Date.now();
  const cooldownMs = cooldownHours * 60 * 60 * 1000;
  const gracePeriodMs = cooldownMs * 0.5;
  const timeSinceLastClaim = now - lastClaimTimestamp;

  if (timeSinceLastClaim > cooldownMs + gracePeriodMs) {
    return 0;
  }

  return 1;
}
