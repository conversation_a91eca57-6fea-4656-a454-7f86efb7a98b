import { getSkinRewardByRarity, type SkinRarity } from '@megami/config/lib/rewards';
import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { eq } from 'drizzle-orm';
import { BaseModel } from '../model';

export type SkinSelect = InferSelectModel<typeof import('../../schema/skins').skins>;
export type SkinInsert = InferInsertModel<typeof import('../../schema/skins').skins>;

export interface SkinCreateParams {
  name: string;
  rarity: SkinRarity;
  url: string;
  authorId: string;
  characterId: string;
}

export class Skins extends BaseModel {
  public async findByName(name: string): Promise<SkinSelect | undefined> {
    return await this.client.instance.query.skins.findFirst({
      where: { name },
      with: {
        character: true,
        author: true,
      },
    });
  }

  /**
   * Find a skin by ID
   */
  public async findById(id: string): Promise<SkinSelect | undefined> {
    return await this.client.instance.query.skins.findFirst({
      where: { id },
      with: {
        character: true,
        author: true,
      },
    });
  }

  /**
   * Get all skins for a character
   */
  public async getByCharacter(characterId: string): Promise<SkinSelect[]> {
    return await this.client.instance.query.skins.findMany({
      where: { characterId },
      with: {
        character: true,
        author: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Get all skins by an author
   */
  public async getByAuthor(authorId: string): Promise<SkinSelect[]> {
    return await this.client.instance.query.skins.findMany({
      where: { authorId },
      with: {
        character: true,
        author: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Create a new skin
   */
  public async create(params: SkinCreateParams): Promise<SkinSelect> {
    const [skin] = await this.client.instance
      .insert(this.schemas.skins)
      .values({
        name: params.name,
        rarity: params.rarity,
        url: params.url,
        authorId: params.authorId,
        characterId: params.characterId,
        approved: false, // Default to not approved
      })
      .returning();

    return skin!;
  }

  /**
   * Approve a skin and give rewards to the author
   */
  public async approve(id: string): Promise<{ skin: SkinSelect; rewardsGiven: boolean } | undefined> {
    // Get the skin with author info first
    const skin = await this.findById(id);
    if (!skin || skin.approved) {
      return;
    }

    // Update skin to approved
    const [updatedSkin] = await this.client.instance
      .update(this.schemas.skins)
      .set({ approved: true, updatedAt: new Date() })
      .where(eq(this.schemas.skins.id, id))
      .returning();

    if (!updatedSkin) {
      return;
    }

    // Give rewards to the author
    let rewardsGiven = false;
    try {
      const rewardConfig = getSkinRewardByRarity(skin.rarity);

      if (rewardConfig) {
        const currencyPromises = rewardConfig.currencies.map(async (currencyReward) => {
          const currency = await this.client.currencies.getCurrencyByName(currencyReward.currency);
          if (!currency) return;

          return this.client.currencies.addToUserBalance(skin.authorId, currency.id, currencyReward.amount);
        });

        await Promise.all(currencyPromises);

        // Give experience reward
        const user = await this.client.users.get(skin.authorId);
        if (user) {
          await this.client.users.addExperience(skin.authorId, rewardConfig.experience.amount);
        }

        rewardsGiven = true;
      }
    } catch {
      // Log error but don't fail the approval - will be handled by logger in calling code
      rewardsGiven = false;
    }

    return { skin: updatedSkin, rewardsGiven };
  }

  /**
   * Reject/unapprove a skin
   */
  public async reject(id: string): Promise<SkinSelect | undefined> {
    const [skin] = await this.client.instance
      .update(this.schemas.skins)
      .set({ approved: false, updatedAt: new Date() })
      .where(eq(this.schemas.skins.id, id))
      .returning();

    return skin;
  }

  /**
   * Get pending skins (not approved)
   */
  public async getPending() {
    return await this.client.instance.query.skins.findMany({
      where: { approved: false },
      with: {
        author: true,
        character: true,
      },
      orderBy: { createdAt: 'asc' },
    });
  }

  /**
   * Get approved skins
   */
  public async getApproved(): Promise<SkinSelect[]> {
    return await this.client.instance.query.skins.findMany({
      where: { approved: true },
      with: {
        character: true,
        author: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }
}
