import { getObject, translate } from '@megami/locale';
import type { SlashCommandSubcommandBuilder } from 'discord.js';
import { defineCommand } from '../../handlers/command';
import view from './modules/view';

export default defineCommand((builder) => ({
  builder: builder
    .setName(translate('commands.profile.name'))
    .setNameLocalizations(getObject('commands.profile.name'))
    .setDescription(translate('commands.profile.description'))
    .setDescriptionLocalizations(getObject('commands.profile.description'))
    .addSubcommand(view.builder as SlashCommandSubcommandBuilder),
  config: {},
  execute: async (interaction) => {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case 'view': {
        await view.execute(interaction);
        break;
      }

      default: {
        await interaction.reply({
          content: translate('errors.general.unknown.description'),
          ephemeral: true,
        });
      }
    }
  },
}));
