import { getObject, translate } from '@megami/locale';
import { <PERSON><PERSON>lags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../../../handlers/command';
import { MegamiContainer } from '../../../../../helpers/containers';
import { defer } from '../../../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.management.modules.schedules.modules.enable.name'))
    .setNameLocalizations(getObject('commands.management.modules.schedules.modules.enable.name'))
    .setDescription(translate('commands.management.modules.schedules.modules.enable.description'))
    .setDescriptionLocalizations(getObject('commands.management.modules.schedules.modules.enable.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.management.modules.schedules.modules.enable.options.task.name'))
        .setNameLocalizations(getObject('commands.management.modules.schedules.modules.enable.options.task.name'))
        .setDescription(translate('commands.management.modules.schedules.modules.enable.options.task.description'))
        .setDescriptionLocalizations(
          getObject('commands.management.modules.schedules.modules.enable.options.task.description')
        )
        .setRequired(true)
        .setAutocomplete(true)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const taskId = interaction.options.getString('task', true);
    const scheduler = interaction.client.scheduler;

    try {
      // Check if task exists
      const task = scheduler.getTask(taskId);
      if (!task) {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Task Not Found**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`Task with ID \`${taskId}\` does not exist.`),
          new TextDisplayBuilder().setContent('Use `/management schedules list` to see available tasks.'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Check if task is already enabled
      if (task.enabled) {
        const isRunning = scheduler.getRunningTasks().some((rt) => rt.id === taskId);
        const nextRun = getNextRunTime(task.cronExpression);

        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('ℹ️ **Task Already Enabled**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent(
            `**Status:** ${isRunning ? '🔄 Currently Running' : '⏸️ Enabled but Idle'}`
          ),
          new TextDisplayBuilder().setContent(`**Schedule:** \`${task.cronExpression}\``),
          new TextDisplayBuilder().setContent(`**Next Run:** ${nextRun}`),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Enable the task
      const success = await scheduler.enableTask(taskId);

      if (success) {
        const nextRun = getNextRunTime(task.cronExpression);
        const willRunOnStart = task.runOnStart ? '\n**Note:** This task will also run immediately on bot startup.' : '';

        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('✅ **Task Enabled Successfully**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`**Task:** ${task.name} (\`${taskId}\`)`),
          new TextDisplayBuilder().setContent(`**Schedule:** \`${task.cronExpression}\``),
          new TextDisplayBuilder().setContent(`**Next Run:** ${nextRun}`),
          new TextDisplayBuilder().setContent(`**Status:** 🔄 Now Running${willRunOnStart}`),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });

        // Log the action
        interaction.client.logger.info(`Task ${taskId} enabled by ${interaction.user.tag}`);
      } else {
        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('❌ **Failed to Enable Task**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`Could not enable task \`${taskId}\`.`),
          new TextDisplayBuilder().setContent(
            'The task may be in an invalid state or the scheduler may be unavailable.'
          ),
          new TextDisplayBuilder().setContent('Please check the logs or contact an administrator.'),
        ]);

        await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Enabling Task**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while enabling the task.'),
        new TextDisplayBuilder().setContent('Please try again or contact support if the issue persists.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error(`Error enabling task ${taskId}:`, error);
    }
  },
  autocomplete: async (interaction) => {
    const focusedValue = interaction.options.getFocused();
    const scheduler = interaction.client.scheduler;

    // Get disabled tasks for autocomplete
    const disabledTasks = scheduler
      .getAllTasks()
      .filter((task) => !task.enabled)
      .filter(
        (task) =>
          task.id.toLowerCase().includes(focusedValue.toLowerCase()) ||
          task.name.toLowerCase().includes(focusedValue.toLowerCase())
      )
      .slice(0, 25);

    const choices = disabledTasks.map((task) => ({
      name: `${task.name} (${task.id}) - ${task.cronExpression}`,
      value: task.id,
    }));

    await interaction.respond(choices);
  },
}));

function getNextRunTime(cronExpression: string): string {
  try {
    // This is a simplified next run calculation
    // In a real implementation, you'd use a cron parser library
    const now = new Date();

    if (cronExpression === '* * * * *') {
      const nextRun = new Date(now.getTime() + 60_000);
      return `<t:${Math.floor(nextRun.getTime() / 1000)}:R>`;
    }

    if (cronExpression === '*/5 * * * *') {
      const minutes = now.getMinutes();
      const nextMinute = Math.ceil(minutes / 5) * 5;
      const nextRun = new Date(now);
      nextRun.setMinutes(nextMinute, 0, 0);
      if (nextRun <= now) nextRun.setHours(nextRun.getHours() + 1, 0, 0, 0);
      return `<t:${Math.floor(nextRun.getTime() / 1000)}:R>`;
    }

    if (cronExpression === '0 0 * * *') {
      const nextRun = new Date(now);
      nextRun.setDate(nextRun.getDate() + 1);
      nextRun.setHours(0, 0, 0, 0);
      return `<t:${Math.floor(nextRun.getTime() / 1000)}:R>`;
    }

    return 'Next execution calculated by scheduler';
  } catch {
    return 'Unknown';
  }
}
