import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { and, eq, ilike, inArray, sql } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import type { ItemRarity, ItemType } from '../../schema/items';
import { BaseModel } from '../model';

export type ItemSelect = InferSelectModel<typeof import('../../schema/items').items>;
export type ItemInsert = InferInsertModel<typeof import('../../schema/items').items>;

export interface ItemCreateParams {
  name: string;
  description: string;
  type: ItemType;
  rarity: ItemRarity;
  icon?: string;
  image?: string;
  stackable?: boolean;
  maxStack?: number;
  tradeable?: boolean;
  sellable?: boolean;
  metadata?: ItemSelect['metadata'];
}

export interface ItemSearchParams {
  name?: string;
  type?: ItemType | ItemType[];
  rarity?: ItemRarity | ItemRarity[];
  stackable?: boolean;
  tradeable?: boolean;
  sellable?: boolean;
  isActive?: boolean;
  isHidden?: boolean;
  limit?: number;
  offset?: number;
}

export class Items extends BaseModel {
  public schema = createSelectSchema(this.schemas.items);

  /**
   * Get an item by ID
   */
  public async get(id: string): Promise<ItemSelect | undefined> {
    return await this.client.instance.query.items.findFirst({
      where: { id },
    });
  }

  /**
   * Get an item by name
   */
  public async getByName(name: string): Promise<ItemSelect | undefined> {
    return await this.client.instance.query.items.findFirst({
      where: { name },
    });
  }

  /**
   * Get multiple items by IDs
   */
  public async getMany(ids: string[]): Promise<ItemSelect[]> {
    if (ids.length === 0) return [];

    return await this.client.instance.query.items.findMany({
      where: inArray(this.schemas.items.id, ids),
    });
  }

  /**
   * Search items with filters
   */
  public async search(params: ItemSearchParams = {}): Promise<ItemSelect[]> {
    const conditions = [];

    // Name search (case-insensitive partial match)
    if (params.name) {
      conditions.push(ilike(this.schemas.items.name, `%${params.name}%`));
    }

    // Type filter
    if (params.type) {
      if (Array.isArray(params.type)) {
        conditions.push(inArray(this.schemas.items.type, params.type));
      } else {
        conditions.push(eq(this.schemas.items.type, params.type));
      }
    }

    // Rarity filter
    if (params.rarity) {
      if (Array.isArray(params.rarity)) {
        conditions.push(inArray(this.schemas.items.rarity, params.rarity));
      } else {
        conditions.push(eq(this.schemas.items.rarity, params.rarity));
      }
    }

    // Boolean filters
    if (params.stackable !== undefined) {
      conditions.push(eq(this.schemas.items.stackable, params.stackable));
    }
    if (params.tradeable !== undefined) {
      conditions.push(eq(this.schemas.items.tradeable, params.tradeable));
    }
    if (params.sellable !== undefined) {
      conditions.push(eq(this.schemas.items.sellable, params.sellable));
    }
    if (params.isActive !== undefined) {
      conditions.push(eq(this.schemas.items.isActive, params.isActive));
    }
    if (params.isHidden !== undefined) {
      conditions.push(eq(this.schemas.items.isHidden, params.isHidden));
    }

    const query = this.client.instance.query.items.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      limit: params.limit || 50,
      offset: params.offset || 0,
      orderBy: [this.schemas.items.name],
    });

    return await query;
  }

  /**
   * Get all items (with optional filters)
   */
  public async getAll(filters: Partial<ItemSearchParams> = {}): Promise<ItemSelect[]> {
    return await this.search({ ...filters, limit: 1000 });
  }

  /**
   * Get items by type
   */
  public async getByType(type: ItemType): Promise<ItemSelect[]> {
    return await this.search({ type });
  }

  /**
   * Get items by rarity
   */
  public async getByRarity(rarity: ItemRarity): Promise<ItemSelect[]> {
    return await this.search({ rarity });
  }

  /**
   * Create a new item
   */
  public async create(params: ItemCreateParams): Promise<ItemSelect> {
    const [item] = await this.client.instance
      .insert(this.schemas.items)
      .values({
        name: params.name,
        description: params.description,
        type: params.type,
        rarity: params.rarity,
        icon: params.icon,
        image: params.image,
        stackable: params.stackable ?? true,
        maxStack: params.maxStack ?? 99,
        tradeable: params.tradeable ?? true,
        sellable: params.sellable ?? true,
        metadata: params.metadata ?? {},
      })
      .returning()
      .execute();

    return item!;
  }

  /**
   * Update an item
   */
  public async update(id: string, updates: Partial<ItemCreateParams>): Promise<ItemSelect | undefined> {
    const [item] = await this.client.instance
      .update(this.schemas.items)
      .set({
        ...updates,
        updatedAt: sql`NOW()`,
      })
      .where(eq(this.schemas.items.id, id))
      .returning()
      .execute();

    return item;
  }

  /**
   * Delete an item (soft delete by setting isActive to false)
   */
  public async delete(id: string): Promise<boolean> {
    const result = await this.client.instance
      .update(this.schemas.items)
      .set({
        isActive: false,
        updatedAt: sql`NOW()`,
      })
      .where(eq(this.schemas.items.id, id))
      .execute();

    return result.rowCount > 0;
  }

  /**
   * Hard delete an item (permanently remove from database)
   */
  public async hardDelete(id: string): Promise<boolean> {
    const result = await this.client.instance.delete(this.schemas.items).where(eq(this.schemas.items.id, id)).execute();

    return result.rowCount > 0;
  }

  /**
   * Check if an item exists
   */
  public async exists(id: string): Promise<boolean> {
    const item = await this.get(id);
    return item !== undefined;
  }

  /**
   * Check if an item name is available
   */
  public async isNameAvailable(name: string, excludeId?: string): Promise<boolean> {
    const conditions = [eq(this.schemas.items.name, name)];

    if (excludeId) {
      conditions.push(sql`${this.schemas.items.id} != ${excludeId}`);
    }

    const item = await this.client.instance.query.items.findFirst({
      where: and(...conditions),
    });

    return item === undefined;
  }

  /**
   * Get item statistics
   */
  public async getStats(): Promise<{
    total: number;
    byType: Record<ItemType, number>;
    byRarity: Record<ItemRarity, number>;
    active: number;
    hidden: number;
  }> {
    const items = await this.getAll();

    const stats = {
      total: items.length,
      byType: {} as Record<ItemType, number>,
      byRarity: {} as Record<ItemRarity, number>,
      active: items.filter((item) => item.isActive).length,
      hidden: items.filter((item) => item.isHidden).length,
    };

    // Count by type
    for (const item of items) {
      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
      stats.byRarity[item.rarity] = (stats.byRarity[item.rarity] || 0) + 1;
    }

    return stats;
  }
}
