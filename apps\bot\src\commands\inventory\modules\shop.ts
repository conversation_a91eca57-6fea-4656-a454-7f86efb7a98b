import type { ItemType } from '@megami/database';
import { getObject, translate } from '@megami/locale';
import { MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from 'discord.js';
import { defineSubCommand } from '../../../handlers/command';
import { MegamiContainer } from '../../../helpers/containers';
import { defer } from '../../../helpers/defer';

export default defineSubCommand((builder) => ({
  builder: builder
    .setName(translate('commands.inventory.modules.shop.name'))
    .setNameLocalizations(getObject('commands.inventory.modules.shop.name'))
    .setDescription(translate('commands.inventory.modules.shop.description'))
    .setDescriptionLocalizations(getObject('commands.inventory.modules.shop.description'))
    .addStringOption((option) =>
      option
        .setName(translate('commands.inventory.modules.shop.options.category.name'))
        .setNameLocalizations(getObject('commands.inventory.modules.shop.options.category.name'))
        .setDescription(translate('commands.inventory.modules.shop.options.category.description'))
        .setDescriptionLocalizations(getObject('commands.inventory.modules.shop.options.category.description'))
        .setRequired(false)
        .addChoices(
          { name: '🛒 All Items', value: 'all' },
          { name: '⚔️ Equipment', value: 'EQUIPMENT' },
          { name: '🧪 Consumables', value: 'CONSUMABLE' },
          { name: '🔨 Materials', value: 'MATERIAL' },
          { name: '💎 Collectibles', value: 'COLLECTIBLE' },
          { name: '💰 Currency', value: 'CURRENCY' },
          { name: '🎁 Gifts', value: 'GIFT' },
          { name: '🗝️ Keys', value: 'KEY' },
          { name: '📦 Miscellaneous', value: 'MISC' }
        )
    )
    .addStringOption((option) =>
      option
        .setName('rarity')
        .setDescription('Filter by item rarity')
        .setRequired(false)
        .addChoices(
          { name: '⚪ Common', value: 'COMMON' },
          { name: '🟢 Uncommon', value: 'UNCOMMON' },
          { name: '🔵 Rare', value: 'RARE' },
          { name: '🟣 Epic', value: 'EPIC' },
          { name: '🟡 Legendary', value: 'LEGENDARY' },
          { name: '🔴 Mythic', value: 'MYTHIC' }
        )
    )
    .addStringOption((option) =>
      option.setName('search').setDescription('Search for specific items by name').setRequired(false).setMaxLength(50)
    ),
  config: {},
  execute: async (interaction) => {
    await defer(interaction, false);

    const user = await interaction.client.database.users.get(interaction.user.id);
    if (!user) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Not Registered**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(translate('errors.user.unregistered.description')),
      ]);

      return await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    }

    const typeFilter = interaction.options.getString('type') || 'all';
    const rarityFilter = interaction.options.getString('rarity');
    const searchQuery = interaction.options.getString('search');

    try {
      // Get user's current rifts for purchase power display
      const userBalance = await interaction.client.database.users.getRifts(user.id);

      // Search for items that are purchasable (have buy price in metadata)
      const searchParams: {
        isActive: boolean;
        limit: number;
        type?: ItemType;
        rarity?: string;
        name?: string;
      } = {
        isActive: true,
        limit: 25,
      };

      if (typeFilter !== 'all') {
        searchParams.type = typeFilter as ItemType;
      }

      if (rarityFilter) {
        searchParams.rarity = rarityFilter;
      }

      if (searchQuery) {
        searchParams.name = searchQuery;
      }

      const allItems = await interaction.client.database.items.search(searchParams);

      // Filter items that have buy prices (are purchasable)
      const purchasableItems = allItems.filter(
        (item) => item.metadata?.economy?.buyPrice && item.metadata.economy.buyPrice > 0
      );

      if (purchasableItems.length === 0) {
        const filterText = getFilterText(typeFilter, rarityFilter, searchQuery);

        const container = new MegamiContainer([
          new TextDisplayBuilder().setContent('🛒 **Item Shop**'),
          new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
          new TextDisplayBuilder().setContent(`No purchasable items found${filterText}.`),
          new TextDisplayBuilder().setContent('Check back later for new items!'),
        ]);

        return await interaction.editReply({
          components: [container],
          flags: MessageFlags.IsComponentsV2,
        });
      }

      // Group items by type
      const itemsByType = purchasableItems.reduce(
        (acc, item) => {
          const type = item.type;
          if (!acc[type]) acc[type] = [];
          acc[type].push(item);
          return acc;
        },
        {} as Record<string, typeof purchasableItems>
      );

      const filterText = getFilterText(typeFilter, rarityFilter, searchQuery);

      const components = [
        new TextDisplayBuilder().setContent(`🛒 **Item Shop${filterText}**`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent(`💰 **Your Rifts:** ${userBalance.toLocaleString()}`),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
      ];

      // Display items by type
      for (const [type, items] of Object.entries(itemsByType)) {
        const typeEmoji = getTypeEmoji(type);
        components.push(new TextDisplayBuilder().setContent(`${typeEmoji} **${type}** (${items.length} items)`));

        // Sort items by price (ascending) and rarity
        const sortedItems = items.sort((a, b) => {
          const priceA = a.metadata?.economy?.buyPrice || 0;
          const priceB = b.metadata?.economy?.buyPrice || 0;
          if (priceA !== priceB) return priceA - priceB;

          const rarityOrder = ['COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY', 'MYTHIC'];
          return rarityOrder.indexOf(a.rarity) - rarityOrder.indexOf(b.rarity);
        });

        for (const item of sortedItems.slice(0, 8)) {
          // Limit to 8 items per type
          const rarityEmoji = getRarityEmoji(item.rarity);
          const price = item.metadata?.economy?.buyPrice || 0;
          const canAfford = userBalance >= price;
          const affordabilityIcon = canAfford ? '✅' : '❌';

          components.push(
            new TextDisplayBuilder().setContent(
              `  ${rarityEmoji} ${item.icon || typeEmoji} **${item.name}** - ${price.toLocaleString()} Rifts ${affordabilityIcon}`
            )
          );

          if (item.description) {
            components.push(new TextDisplayBuilder().setContent(`    └ ${item.description}`));
          }
        }

        if (items.length > 8) {
          components.push(new TextDisplayBuilder().setContent(`  ... and ${items.length - 8} more items`));
        }

        components.push(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small));
      }

      // Add purchase instructions
      components.push(
        new TextDisplayBuilder().setContent('💡 **How to Purchase:**'),
        new TextDisplayBuilder().setContent('Use `/shop buy <item-name>` to purchase items.'),
        new TextDisplayBuilder().setContent('✅ = You can afford | ❌ = Insufficient rifts')
      );

      const container = new MegamiContainer(components);
      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });
    } catch (error) {
      const container = new MegamiContainer([
        new TextDisplayBuilder().setContent('❌ **Error Loading Shop**'),
        new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
        new TextDisplayBuilder().setContent('An unexpected error occurred while loading the shop.'),
        new TextDisplayBuilder().setContent('Please try again or contact support.'),
      ]);

      await interaction.editReply({
        components: [container],
        flags: MessageFlags.IsComponentsV2,
      });

      interaction.client.logger.error('Error loading shop:', error);
    }
  },
}));

function getFilterText(typeFilter: string, rarityFilter: string | null, searchQuery: string | null): string {
  const filters: string[] = [];

  if (typeFilter !== 'all') {
    filters.push(typeFilter.toLowerCase());
  }

  if (rarityFilter) {
    filters.push(rarityFilter.toLowerCase());
  }

  if (searchQuery) {
    filters.push(`"${searchQuery}"`);
  }

  return filters.length > 0 ? ` (${filters.join(', ')})` : '';
}

function getTypeEmoji(type: string): string {
  const emojis: Record<string, string> = {
    EQUIPMENT: '⚔️',
    CONSUMABLE: '🧪',
    MATERIAL: '🔨',
    COLLECTIBLE: '💎',
    CURRENCY: '💰',
    GIFT: '🎁',
    KEY: '🗝️',
    MISC: '📦',
  };
  return emojis[type] || '📦';
}

function getRarityEmoji(rarity: string): string {
  const emojis: Record<string, string> = {
    COMMON: '⚪',
    UNCOMMON: '🟢',
    RARE: '🔵',
    EPIC: '🟣',
    LEGENDARY: '🟡',
    MYTHIC: '🔴',
  };
  return emojis[rarity] || '⚪';
}
